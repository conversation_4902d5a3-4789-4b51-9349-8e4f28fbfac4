import asyncio
from playwright.async_api import async_playwright

async def main():
    print("Free Canva Pro by MrShadowDev")
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        await page.set_extra_http_headers({'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.67 Safari/537.36'})
        await page.goto("https://bingotingo.com/best-social-media-platforms/")
        await page.wait_for_selector("h2:has-text('Free Guide')")
        print("Trying to find canva pro for you! Please wait 60s...")
        await page.wait_for_selector("#download", timeout=70000)
        async with page.expect_popup() as popup_info:
            await page.click("#download")
        new_page = await popup_info.value
        href_link = await new_page.get_attribute("a:has-text('GET HERE')", "href")
        with open("canva_pro_link.txt", "w") as f:
            f.write(href_link)
            print("Canva Pro Found!")
        await browser.close()

asyncio.run(main())
